import os
import json
import uuid
from typing import Dict, Any, List, TypedDict, Annotated

# Simplified imports to avoid compatibility issues
from langchain_core.messages import HumanMessage, SystemMessage
from langchain_openai import ChatOpenAI

# Simple workflow state management without LangGraph dependencies
LANGGRAPH_AVAILABLE = True


def add_messages(a, b):
    return a + b


END = "END"


from app.models.project_models import (
    IntelligentProjectRequest,
    ProjectAnalysis,
    TechnologyStack,
    ModificationSuggestion,
    FileModification,
)


class ProjectGenerationState(TypedDict):
    """State for the project generation workflow"""

    messages: Annotated[List, add_messages]
    request: IntelligentProjectRequest
    analysis: ProjectAnalysis
    project_structure: Dict[str, Any]
    generated_files: Dict[str, str]
    validation_results: Dict[str, Any]
    current_step: str
    progress: int
    error_message: str


class LangGraphProjectGenerator:
    """Enhanced project generator using LangGraph for workflow orchestration"""

    def __init__(self):
        if not LANGGRAPH_AVAILABLE:
            raise Exception(
                "LangGraph is not available. Please install the required dependencies."
            )

        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.llm = ChatOpenAI(
            api_key=self.openai_api_key, model="gpt-4-1106-preview", temperature=0.1
        )
        self.workflow = self._create_workflow()

    def _create_workflow(self):
        """Create a simple workflow for project generation"""
        # Simple workflow implementation without LangGraph dependencies
        return self

    async def analyze_project_requirements(
        self, request: IntelligentProjectRequest
    ) -> ProjectAnalysis:
        """Analyze project requirements using AI"""

        analysis_prompt = f"""
        You are an expert software architect. Analyze this project request and provide a comprehensive technical analysis.

        PROJECT REQUEST: {request.prompt}

        USER PREFERENCES:
        - Preferred Technologies: {request.preferred_technologies or 'None'}
        - Target Platforms: {request.target_platform or 'Not specified'}
        - Complexity Level: {request.complexity_level}
        - Include Tests: {request.include_tests}
        - Include Documentation: {request.include_documentation}

        Provide analysis in JSON format with:
        - project_type (fullstack/frontend/backend/mobile/api)
        - suggested_name
        - technology_stack (frontend, backend, database, styling, testing)
        - architecture_pattern
        - estimated_complexity
        - key_features (list)
        - technical_requirements (list)
        - dependencies
        - reasoning

        Return ONLY valid JSON.
        """

        messages = [
            SystemMessage(
                content="You are an expert software architect providing technical analysis."
            ),
            HumanMessage(content=analysis_prompt),
        ]

        response = await self.llm.ainvoke(messages)

        try:
            analysis_data = json.loads(response.content)

            # Create TechnologyStack
            tech_stack_data = analysis_data.get("technology_stack", {})
            technology_stack = TechnologyStack(**tech_stack_data)

            # Create ProjectAnalysis
            return ProjectAnalysis(
                project_type=analysis_data.get("project_type", "fullstack"),
                suggested_name=analysis_data.get(
                    "suggested_name", request.project_name or "ai-project"
                ),
                technology_stack=technology_stack,
                architecture_pattern=analysis_data.get("architecture_pattern", "MVC"),
                estimated_complexity=analysis_data.get(
                    "estimated_complexity", request.complexity_level
                ),
                key_features=analysis_data.get("key_features", []),
                technical_requirements=analysis_data.get("technical_requirements", []),
                suggested_folder_structure=analysis_data.get(
                    "suggested_folder_structure", {}
                ),
                dependencies=analysis_data.get("dependencies", {}),
                reasoning=analysis_data.get("reasoning", "AI analysis completed"),
            )
        except Exception as e:
            raise Exception(f"Failed to analyze project requirements: {str(e)}")

    async def suggest_modifications(
        self,
        project_id: str,
        modification_prompt: str,
        current_files: Dict[str, str],
        ai_provider=None,
    ) -> List[ModificationSuggestion]:
        """Generate modification suggestions using AI"""

        suggestion_prompt = f"""
        Based on the current project files and the modification request, suggest specific improvements.

        MODIFICATION REQUEST: {modification_prompt}

        CURRENT FILES: {len(current_files)} files in the project

        Provide 3-5 specific modification suggestions in JSON format:
        {{
            "suggestions": [
                {{
                    "suggestion_id": "unique_id",
                    "title": "Brief title",
                    "description": "Detailed description",
                    "category": "feature|bugfix|improvement|refactor",
                    "files": [
                        {{
                            "file_path": "path/to/file",
                            "action": "create|modify|delete",
                            "content": "file content if creating/modifying"
                        }}
                    ],
                    "estimated_effort": "small|medium|large",
                    "impact": "low|medium|high",
                    "reasoning": "Why this suggestion is valuable"
                }}
            ]
        }}

        Return ONLY valid JSON.
        """

        messages = [
            SystemMessage(
                content="You are an expert developer providing code improvement suggestions."
            ),
            HumanMessage(content=suggestion_prompt),
        ]

        response = await self.llm.ainvoke(messages)

        try:
            data = json.loads(response.content)
            suggestions = []

            for suggestion_data in data.get("suggestions", []):
                files = []
                for file_data in suggestion_data.get("files", []):
                    files.append(FileModification(**file_data))

                suggestion = ModificationSuggestion(
                    suggestion_id=suggestion_data.get(
                        "suggestion_id", str(uuid.uuid4())
                    ),
                    title=suggestion_data.get("title", "Modification"),
                    description=suggestion_data.get("description", ""),
                    category=suggestion_data.get("category", "feature"),
                    files=files,
                    estimated_effort=suggestion_data.get("estimated_effort", "medium"),
                    impact=suggestion_data.get("impact", "medium"),
                    reasoning=suggestion_data.get("reasoning", ""),
                )
                suggestions.append(suggestion)

            return suggestions
        except Exception as e:
            raise Exception(f"Failed to generate modification suggestions: {str(e)}")

    async def generate_project(
        self, request: IntelligentProjectRequest
    ) -> Dict[str, Any]:
        """Generate a complete project using simplified workflow"""

        # Create initial state
        state = {
            "messages": [],
            "request": request,
            "analysis": None,
            "project_structure": {},
            "generated_files": {},
            "validation_results": {},
            "current_step": "Starting project generation",
            "progress": 0,
            "error_message": "",
        }

        # Run simplified workflow steps
        state = await self._analyze_requirements(state)
        state = await self._design_architecture(state)
        state = await self._generate_structure(state)
        state = await self._generate_frontend(state)
        state = await self._generate_backend(state)
        state = await self._generate_config(state)
        state = await self._validate_project(state)
        state = await self._finalize_project(state)

        return {
            "files": state["generated_files"],
            "analysis": state["analysis"],
            "structure": state["project_structure"],
            "validation": state["validation_results"],
            "instructions": self._generate_setup_instructions(state["analysis"]),
            "next_steps": self._generate_next_steps(state["analysis"]),
        }

    async def _analyze_requirements(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze project requirements from the user prompt"""

        state["current_step"] = "Analyzing project requirements"
        state["progress"] = 10

        analysis_prompt = f"""
        You are an expert software architect. Analyze this project request and provide a comprehensive technical analysis.
        
        PROJECT REQUEST: {state["request"].prompt}
        
        USER PREFERENCES:
        - Preferred Technologies: {state["request"].preferred_technologies or 'None'}
        - Target Platforms: {state["request"].target_platform or 'Not specified'}
        - Complexity Level: {state["request"].complexity_level}
        - Include Tests: {state["request"].include_tests}
        - Include Documentation: {state["request"].include_documentation}
        
        Provide analysis in JSON format with:
        - project_type (fullstack/frontend/backend/mobile/api)
        - suggested_name
        - technology_stack (frontend, backend, database, styling, testing)
        - architecture_pattern
        - estimated_complexity
        - key_features (list)
        - technical_requirements (list)
        - dependencies
        - reasoning
        
        Return ONLY valid JSON.
        """

        messages = [
            SystemMessage(
                content="You are an expert software architect providing technical analysis."
            ),
            HumanMessage(content=analysis_prompt),
        ]

        response = await self.llm.ainvoke(messages)

        try:
            analysis_data = json.loads(response.content)

            # Create TechnologyStack
            tech_stack_data = analysis_data.get("technology_stack", {})
            technology_stack = TechnologyStack(**tech_stack_data)

            # Create ProjectAnalysis
            analysis = ProjectAnalysis(
                project_type=analysis_data.get("project_type", "fullstack"),
                suggested_name=analysis_data.get(
                    "suggested_name", state["request"].project_name or "ai-project"
                ),
                technology_stack=technology_stack,
                architecture_pattern=analysis_data.get("architecture_pattern", "MVC"),
                estimated_complexity=analysis_data.get(
                    "estimated_complexity", state["request"].complexity_level
                ),
                key_features=analysis_data.get("key_features", []),
                technical_requirements=analysis_data.get("technical_requirements", []),
                suggested_folder_structure=analysis_data.get(
                    "suggested_folder_structure", {}
                ),
                dependencies=analysis_data.get("dependencies", {}),
                reasoning=analysis_data.get("reasoning", "AI analysis completed"),
            )

            state["analysis"] = analysis
            state["messages"].append(
                HumanMessage(content=f"Analysis completed: {analysis.suggested_name}")
            )

        except Exception as e:
            # Re-raise the exception instead of using fallback
            raise Exception(f"Failed to analyze project requirements: {str(e)}")

        return state

    async def _design_architecture(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Design the project architecture and folder structure"""

        state["current_step"] = "Designing project architecture"
        state["progress"] = 20

        analysis = state["analysis"]

        # Determine project structure based on analysis
        needs_frontend = (
            analysis.project_type in ["fullstack", "frontend"]
            or analysis.technology_stack.frontend
        )
        needs_backend = (
            analysis.project_type in ["fullstack", "backend", "api"]
            or analysis.technology_stack.backend
        )

        project_structure = {
            "type": analysis.project_type,
            "name": analysis.suggested_name,
            "needs_frontend": needs_frontend,
            "needs_backend": needs_backend,
            "architecture": analysis.architecture_pattern,
            "folders": {},
        }

        if needs_frontend:
            project_structure["folders"]["frontend"] = {
                "src": ["components", "pages", "services", "hooks", "utils", "types"],
                "public": ["assets", "icons"],
                "config": ["vite.config.ts", "tailwind.config.js", "tsconfig.json"],
            }

        if needs_backend:
            project_structure["folders"]["backend"] = {
                "app": ["models", "services", "routes", "utils", "middleware"],
                "tests": ["unit", "integration"],
                "config": ["requirements.txt", "main.py", ".env.example"],
            }

        # Add root level files
        project_structure["folders"]["root"] = [
            "README.md",
            ".gitignore",
            "docker-compose.yml",
        ]

        state["project_structure"] = project_structure
        state["messages"].append(
            HumanMessage(
                content=f"Architecture designed: {analysis.architecture_pattern}"
            )
        )

        return state

    async def _generate_structure(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate the basic project structure and configuration files"""

        state["current_step"] = "Generating project structure"
        state["progress"] = 30

        analysis = state["analysis"]
        structure = state["project_structure"]
        files = {}

        # Generate README.md
        files["README.md"] = self._generate_readme(analysis)

        # Generate .gitignore
        files[".gitignore"] = self._generate_gitignore(structure)

        # Generate docker-compose.yml if needed
        if structure["needs_frontend"] and structure["needs_backend"]:
            files["docker-compose.yml"] = self._generate_docker_compose(analysis)

        state["generated_files"].update(files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(files)} structure files")
        )

        return state

    async def _generate_frontend(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate frontend files and components"""

        state["current_step"] = "Generating frontend components"
        state["progress"] = 50

        analysis = state["analysis"]
        structure = state["project_structure"]

        if not structure["needs_frontend"]:
            return state

        # Generate frontend files using AI
        frontend_files = await self._generate_ai_frontend(analysis, state["request"])
        state["generated_files"].update(frontend_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(frontend_files)} frontend files")
        )

        return state

    async def _generate_backend(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate backend files and API endpoints"""

        state["current_step"] = "Generating backend API"
        state["progress"] = 70

        analysis = state["analysis"]
        structure = state["project_structure"]

        if not structure["needs_backend"]:
            return state

        # Generate backend files using AI
        backend_files = await self._generate_ai_backend(analysis, state["request"])
        state["generated_files"].update(backend_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(backend_files)} backend files")
        )

        return state

    async def _generate_config(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Generate configuration and deployment files"""

        state["current_step"] = "Generating configuration files"
        state["progress"] = 85

        analysis = state["analysis"]
        structure = state["project_structure"]

        config_files = {}

        # Generate deployment configurations
        if structure["needs_frontend"] and structure["needs_backend"]:
            config_files["Makefile"] = self._generate_makefile(analysis)

        # Generate CI/CD if requested
        if state["request"].include_ci_cd:
            config_files[".github/workflows/ci.yml"] = self._generate_github_actions(
                analysis
            )

        # Generate environment files
        config_files[".env.example"] = self._generate_env_example(analysis)

        state["generated_files"].update(config_files)
        state["messages"].append(
            HumanMessage(content=f"Generated {len(config_files)} config files")
        )

        return state

    async def _validate_project(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the generated project structure and files"""

        state["current_step"] = "Validating project structure"
        state["progress"] = 95

        validation_results = {
            "total_files": len(state["generated_files"]),
            "has_frontend": any(
                path.startswith("frontend/") for path in state["generated_files"]
            ),
            "has_backend": any(
                path.startswith("backend/") for path in state["generated_files"]
            ),
            "has_readme": "README.md" in state["generated_files"],
            "has_gitignore": ".gitignore" in state["generated_files"],
            "missing_files": [],
            "validation_passed": True,
        }

        # Check for essential files
        essential_files = ["README.md"]

        if state["project_structure"]["needs_frontend"]:
            essential_files.extend(
                [
                    "frontend/package.json",
                    "frontend/src/App.tsx",
                    "frontend/src/main.tsx",
                ]
            )

        if state["project_structure"]["needs_backend"]:
            essential_files.extend(["backend/requirements.txt", "backend/main.py"])

        for file in essential_files:
            if file not in state["generated_files"]:
                validation_results["missing_files"].append(file)
                validation_results["validation_passed"] = False

        state["validation_results"] = validation_results
        state["messages"].append(
            HumanMessage(
                content=f"Validation completed: {validation_results['validation_passed']}"
            )
        )

        return state

    async def _finalize_project(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """Finalize the project generation"""

        state["current_step"] = "Project generation completed"
        state["progress"] = 100

        # Add final summary
        summary = {
            "project_name": state["analysis"].suggested_name,
            "project_type": state["analysis"].project_type,
            "total_files": len(state["generated_files"]),
            "technologies": {
                "frontend": state["analysis"].technology_stack.frontend,
                "backend": state["analysis"].technology_stack.backend,
                "database": state["analysis"].technology_stack.database,
            },
            "validation_passed": state["validation_results"].get(
                "validation_passed", False
            ),
        }

        state["messages"].append(
            HumanMessage(content=f"Project generation completed: {summary}")
        )

        return state

    # Helper methods for generating file content

    def _generate_readme(self, analysis: ProjectAnalysis) -> str:
        """Generate README.md content"""
        return f"""# {analysis.suggested_name}

## Description
{analysis.reasoning}

## Features
{chr(10).join(f"- {feature}" for feature in analysis.key_features)}

## Technology Stack
- **Frontend**: {', '.join(analysis.technology_stack.frontend or [])}
- **Backend**: {', '.join(analysis.technology_stack.backend or [])}
- **Database**: {', '.join(analysis.technology_stack.database or [])}
- **Styling**: {', '.join(analysis.technology_stack.styling or [])}

## Architecture
- **Pattern**: {analysis.architecture_pattern}
- **Complexity**: {analysis.estimated_complexity}

## Setup Instructions

### Prerequisites
- Node.js 18+ (for frontend)
- Python 3.9+ (for backend)
- Git

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd {analysis.suggested_name}
```

2. Install frontend dependencies:
```bash
cd frontend
npm install
```

3. Install backend dependencies:
```bash
cd ../backend
pip install -r requirements.txt
```

4. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

### Running the Application

1. Start the backend server:
```bash
cd backend
python main.py
```

2. Start the frontend development server:
```bash
cd frontend
npm run dev
```

3. Open your browser and navigate to `http://localhost:3000`

## Project Structure
```
{analysis.suggested_name}/
├── frontend/          # React frontend application
│   ├── src/
│   │   ├── components/
│   │   ├── pages/
│   │   └── services/
│   └── package.json
├── backend/           # FastAPI backend application
│   ├── app/
│   │   ├── models/
│   │   ├── routes/
│   │   └── services/
│   └── requirements.txt
└── README.md
```

## Contributing
1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Submit a pull request

## License
MIT License

---
Generated by AI Project Generator
"""

    def _generate_gitignore(self, structure: Dict[str, Any]) -> str:
        """Generate .gitignore content"""
        gitignore_content = """# Dependencies
node_modules/
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
venv/
.venv/
pip-log.txt
pip-delete-this-directory.txt

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
tmp/
temp/
"""

        if structure.get("needs_frontend"):
            gitignore_content += """
# Frontend specific
frontend/dist/
frontend/build/
frontend/.next/
frontend/out/
"""

        if structure.get("needs_backend"):
            gitignore_content += """
# Backend specific
backend/__pycache__/
backend/*.pyc
backend/.pytest_cache/
backend/instance/
backend/.coverage
backend/htmlcov/
"""

        return gitignore_content

    async def _generate_ai_frontend(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest
    ) -> Dict[str, str]:
        """Generate frontend files using AI based on analysis"""

        frontend_prompt = f"""
        Generate a complete React frontend application based on this analysis:

        Project: {analysis.suggested_name}
        Type: {analysis.project_type}
        Features: {', '.join(analysis.key_features)}
        Tech Stack: {', '.join(analysis.technology_stack.frontend or [])}
        Styling: {', '.join(analysis.technology_stack.styling or [])}

        Generate the following files with complete, production-ready code:
        1. package.json - with all necessary dependencies
        2. vite.config.ts - Vite configuration
        3. index.html - main HTML file
        4. src/main.tsx - React entry point
        5. src/App.tsx - main App component with routing
        6. src/components/Header.tsx - header component
        7. src/components/MainContent.tsx - main content component
        8. src/components/Footer.tsx - footer component
        9. src/index.css - global styles with Tailwind
        10. tailwind.config.js - Tailwind configuration
        11. tsconfig.json - TypeScript configuration
        12. postcss.config.js - PostCSS configuration

        Requirements:
        - Use React 18 with TypeScript
        - Use Vite as build tool
        - Use Tailwind CSS for styling
        - Include React Router for navigation
        - Make it responsive and modern
        - Include proper TypeScript types
        - Add loading states and error handling

        Return as JSON with file paths as keys and file contents as values.
        """

        messages = [
            SystemMessage(
                content="You are an expert React developer. Generate complete, production-ready frontend code."
            ),
            HumanMessage(content=frontend_prompt),
        ]

        response = await self.llm.ainvoke(messages)

        try:
            # Parse the AI response to extract files
            files_data = json.loads(response.content)
            return files_data
        except Exception as e:
            # If AI generation fails, raise an error instead of using fallback
            raise Exception(f"Failed to generate frontend files: {str(e)}")

    async def _generate_ai_backend(
        self, analysis: ProjectAnalysis, request: IntelligentProjectRequest
    ) -> Dict[str, str]:
        """Generate backend files using AI based on analysis"""

        backend_prompt = f"""
        Generate a complete FastAPI backend application based on this analysis:

        Project: {analysis.suggested_name}
        Type: {analysis.project_type}
        Features: {', '.join(analysis.key_features)}
        Tech Stack: {', '.join(analysis.technology_stack.backend or [])}
        Database: {', '.join(analysis.technology_stack.database or [])}

        Generate the following files with complete, production-ready code:
        1. requirements.txt - with all necessary dependencies
        2. main.py - FastAPI application with CORS and basic endpoints
        3. app/__init__.py - empty init file
        4. app/models.py - SQLAlchemy models
        5. app/database.py - database configuration
        6. app/schemas.py - Pydantic schemas
        7. .env.example - environment variables template
        8. Dockerfile - for containerization

        Requirements:
        - Use FastAPI with Python 3.11+
        - Include SQLAlchemy for database ORM
        - Use Pydantic for data validation
        - Include CORS middleware for frontend integration
        - Add proper error handling
        - Include health check endpoint
        - Make it production-ready
        - Include proper typing

        Return as JSON with file paths as keys and file contents as values.
        """

        messages = [
            SystemMessage(
                content="You are an expert Python/FastAPI developer. Generate complete, production-ready backend code."
            ),
            HumanMessage(content=backend_prompt),
        ]

        response = await self.llm.ainvoke(messages)

        try:
            # Parse the AI response to extract files
            files_data = json.loads(response.content)
            return files_data
        except Exception as e:
            # If AI generation fails, raise an error instead of using fallback
            raise Exception(f"Failed to generate backend files: {str(e)}")

    def _generate_docker_compose(self, analysis: ProjectAnalysis) -> str:
        """Generate docker-compose.yml"""
        project_name = analysis.suggested_name.lower().replace(" ", "-")

        return f"""version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=**************************************/{project_name}
    depends_on:
      - db
    volumes:
      - ./backend:/app

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB={project_name}
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
"""

    def _generate_makefile(self, analysis: ProjectAnalysis) -> str:
        """Generate Makefile for project management"""
        return """# Project Management Makefile

.PHONY: help install dev build test clean docker-up docker-down

help:
	@echo "Available commands:"
	@echo "  install     - Install all dependencies"
	@echo "  dev         - Start development servers"
	@echo "  build       - Build the project"
	@echo "  test        - Run tests"
	@echo "  clean       - Clean build artifacts"
	@echo "  docker-up   - Start with Docker Compose"
	@echo "  docker-down - Stop Docker Compose"

install:
	@echo "Installing frontend dependencies..."
	cd frontend && npm install
	@echo "Installing backend dependencies..."
	cd backend && pip install -r requirements.txt

dev:
	@echo "Starting development servers..."
	@echo "Backend will run on http://localhost:8000"
	@echo "Frontend will run on http://localhost:3000"
	@make -j2 dev-backend dev-frontend

dev-backend:
	cd backend && python main.py

dev-frontend:
	cd frontend && npm run dev

build:
	@echo "Building frontend..."
	cd frontend && npm run build
	@echo "Build complete!"

test:
	@echo "Running frontend tests..."
	cd frontend && npm test
	@echo "Running backend tests..."
	cd backend && python -m pytest

clean:
	@echo "Cleaning build artifacts..."
	rm -rf frontend/dist
	rm -rf frontend/build
	rm -rf backend/__pycache__
	rm -rf backend/.pytest_cache

docker-up:
	docker-compose up -d

docker-down:
	docker-compose down
"""

    def _generate_env_example(self, analysis: ProjectAnalysis) -> str:
        """Generate .env.example file"""
        return f"""# {analysis.suggested_name} Environment Variables

# Development
NODE_ENV=development
DEBUG=true

# API Configuration
API_URL=http://localhost:8000
API_TIMEOUT=30000

# Database
DATABASE_URL=sqlite:///./app.db

# Security
JWT_SECRET=your-jwt-secret-here
ENCRYPTION_KEY=your-encryption-key-here

# External Services
# Add your API keys and service configurations here
"""

    def _generate_github_actions(self, analysis: ProjectAnalysis) -> str:
        """Generate GitHub Actions CI/CD workflow"""
        return f"""name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: frontend/package-lock.json

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci

    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt

    - name: Run frontend tests
      run: |
        cd frontend
        npm test

    - name: Run backend tests
      run: |
        cd backend
        python -m pytest

    - name: Build frontend
      run: |
        cd frontend
        npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v3

    - name: Deploy to production
      run: |
        echo "Add your deployment steps here"
        # Example: Deploy to your hosting service
"""

    def _generate_setup_instructions(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate setup instructions for the project"""
        instructions = [
            "1. Clone or download the generated project files",
            "2. Navigate to the project directory",
            "3. Install frontend dependencies: cd frontend && npm install",
            "4. Install backend dependencies: cd backend && pip install -r requirements.txt",
            "5. Copy .env.example to .env and configure your environment variables",
            "6. Start the backend server: cd backend && python main.py",
            "7. Start the frontend development server: cd frontend && npm run dev",
            "8. Open your browser and navigate to http://localhost:3000",
        ]

        if (
            analysis.technology_stack.database
            and "postgresql" in str(analysis.technology_stack.database).lower()
        ):
            instructions.insert(
                5, "5a. Set up PostgreSQL database and update DATABASE_URL in .env"
            )

        return instructions

    def _generate_next_steps(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate next steps for project development"""
        return [
            "🎯 Customize the UI components to match your design requirements",
            "🔧 Add your specific business logic to the backend API endpoints",
            "🗄️ Set up your database schema and models",
            "🔐 Implement authentication and authorization if needed",
            "📝 Add comprehensive tests for your components and API endpoints",
            "🚀 Configure deployment to your preferred hosting platform",
            "📚 Update documentation with your specific requirements",
            "🔍 Add monitoring and logging for production use",
        ]
