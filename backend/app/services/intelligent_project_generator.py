import uuid
import asyncio
import zipfile
import json
from typing import Dict, Any, Optional, List
from pathlib import Path

from app.models.project_models import (
    IntelligentProjectRequest,
    IntelligentProjectResponse,
    EnhancedGenerationStatus,
    ProjectAnalysis,
    ModificationRequest,
    ModificationResponse,
    ModificationSuggestion,
    ApplyModificationRequest,
)
from app.services.langgraph_project_generator import LangGraphProjectGenerator
from app.utils.file_utils import FileUtils


class IntelligentProjectGenerator:
    """Enhanced project generator with intelligent AI capabilities"""

    def __init__(self):
        self.file_utils = FileUtils()
        self.langgraph_generator = LangGraphProjectGenerator()

        self.generation_status: Dict[str, EnhancedGenerationStatus] = {}
        self.project_analyses: Dict[str, ProjectAnalysis] = {}
        self.project_files: Dict[str, Dict[str, str]] = {}  # project_id -> files
        self.modification_suggestions: Dict[str, List[ModificationSuggestion]] = {}
        self.output_dir = Path("generated_projects")
        self.output_dir.mkdir(exist_ok=True)

    async def generate_intelligent_project(
        self, request: IntelligentProjectRequest
    ) -> IntelligentProjectResponse:
        """Generate project using intelligent AI analysis"""
        project_id = str(uuid.uuid4())

        # Initialize enhanced status tracking
        self.generation_status[project_id] = EnhancedGenerationStatus(
            project_id=project_id,
            status="analyzing",
            progress=0,
            current_step="Analyzing project requirements with AI",
            phase="analysis",
            can_modify=False,
        )

        try:
            # Start generation in background
            asyncio.create_task(
                self._generate_intelligent_project_async(project_id, request)
            )

            return IntelligentProjectResponse(
                project_id=project_id,
                status="analyzing",
                message="Intelligent project generation started successfully",
            )
        except Exception as e:
            self.generation_status[project_id].status = "failed"
            self.generation_status[project_id].error_message = str(e)

            return IntelligentProjectResponse(
                project_id=project_id,
                status="failed",
                message=f"Failed to start intelligent project generation: {str(e)}",
            )

    async def _generate_intelligent_project_async(
        self, project_id: str, request: IntelligentProjectRequest
    ):
        """Async intelligent project generation process"""
        try:
            status = self.generation_status[project_id]

            # Phase 1: LangGraph Analysis
            status.current_step = "Analyzing project requirements with LangGraph"
            status.progress = 10
            status.phase = "analysis"

            print(f"DEBUG: Starting LangGraph analysis for project {project_id}")
            analysis = await self.langgraph_generator.analyze_project_requirements(
                request
            )
            print(f"DEBUG: Analysis completed successfully: {analysis.suggested_name}")

            self.project_analyses[project_id] = analysis
            status.analysis = analysis
            print(
                f"DEBUG: Analysis stored. Available analyses: {list(self.project_analyses.keys())}"
            )

            # Phase 2: Project Generation
            status.current_step = "Generating project structure and code"
            status.progress = 30
            status.phase = "generation"
            status.status = "generating"

            print(f"DEBUG: Starting LangGraph code generation for project {project_id}")
            ai_result = await self.langgraph_generator.generate_project(request)
            print(
                f"DEBUG: LangGraph generation completed with {len(ai_result.get('files', {}))} files"
            )

            # Phase 3: File Creation
            status.current_step = "Creating project files"
            status.progress = 60

            project_dir = self.output_dir / project_id
            project_dir.mkdir(exist_ok=True)

            generated_files = ai_result.get("files", {})
            self.project_files[project_id] = generated_files

            # Write files to disk atomically to minimize reload triggers
            import tempfile
            import shutil

            for file_path, content in generated_files.items():
                full_path = project_dir / file_path
                full_path.parent.mkdir(parents=True, exist_ok=True)

                # Write to temporary file first, then move to final location
                # This reduces the chance of triggering file watchers during write
                with tempfile.NamedTemporaryFile(
                    mode="w",
                    encoding="utf-8",
                    delete=False,
                    dir=full_path.parent,
                    suffix=".tmp",
                ) as temp_file:
                    temp_file.write(content)
                    temp_file.flush()

                # Atomic move to final location
                shutil.move(temp_file.name, full_path)

            # Phase 4: Generate Initial Suggestions
            status.current_step = "Generating improvement suggestions"
            status.progress = 80
            status.phase = "review"

            # Generate initial modification suggestions
            initial_suggestions = await self._generate_initial_suggestions(
                project_id, analysis, generated_files
            )
            self.modification_suggestions[project_id] = initial_suggestions
            status.pending_modifications = initial_suggestions

            # Phase 5: Create ZIP file
            status.current_step = "Creating downloadable package"
            status.progress = 90

            zip_path = await self._create_zip_file(project_id, project_dir)

            # Phase 6: Complete
            status.status = "completed"
            status.progress = 100
            status.current_step = "Project generation completed"
            status.can_modify = True

        except Exception as e:
            status.status = "failed"
            status.error_message = str(e)
            status.current_step = f"Generation failed: {str(e)}"

    async def request_modifications(
        self, modification_request: ModificationRequest
    ) -> ModificationResponse:
        """Request modifications to an existing project"""
        project_id = modification_request.project_id
        modification_id = str(uuid.uuid4())

        if project_id not in self.project_files:
            raise ValueError("Project not found")

        try:
            current_files = self.project_files[project_id]
            suggestions = await self.langgraph_generator.suggest_modifications(
                project_id,
                modification_request.modification_prompt,
                current_files,
                modification_request.ai_provider,
            )

            # Store suggestions
            if project_id not in self.modification_suggestions:
                self.modification_suggestions[project_id] = []

            self.modification_suggestions[project_id].extend(suggestions)

            # Update status
            if project_id in self.generation_status:
                self.generation_status[project_id].pending_modifications = (
                    self.modification_suggestions[project_id]
                )

            # Auto-apply if requested
            if modification_request.apply_immediately and suggestions:
                await self._apply_modifications(
                    project_id, [s.suggestion_id for s in suggestions]
                )
                status = "applied"
            else:
                status = "ready"

            return ModificationResponse(
                modification_id=modification_id,
                project_id=project_id,
                suggestions=suggestions,
                status=status,
                message=f"Generated {len(suggestions)} modification suggestions",
            )

        except Exception as e:
            return ModificationResponse(
                modification_id=modification_id,
                project_id=project_id,
                suggestions=[],
                status="failed",
                message=f"Failed to generate modifications: {str(e)}",
            )

    async def apply_modifications(
        self, apply_request: ApplyModificationRequest
    ) -> Dict[str, Any]:
        """Apply specific modifications to a project"""
        project_id = apply_request.project_id

        if project_id not in self.project_files:
            raise ValueError("Project not found")

        try:
            applied_modifications = await self._apply_modifications(
                project_id, apply_request.modification_ids, apply_request.custom_changes
            )

            # Update status
            if project_id in self.generation_status:
                status = self.generation_status[project_id]
                if not status.applied_modifications:
                    status.applied_modifications = []
                status.applied_modifications.extend(apply_request.modification_ids)

                # Remove applied suggestions from pending
                if status.pending_modifications:
                    status.pending_modifications = [
                        s
                        for s in status.pending_modifications
                        if s.suggestion_id not in apply_request.modification_ids
                    ]

            # Recreate ZIP file with modifications
            project_dir = self.output_dir / project_id
            await self._update_project_files(project_id, project_dir)
            zip_path = await self._create_zip_file(project_id, project_dir)

            return {
                "success": True,
                "applied_modifications": applied_modifications,
                "message": f"Applied {len(applied_modifications)} modifications successfully",
            }

        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"Failed to apply modifications: {str(e)}",
            }

    async def _generate_initial_suggestions(
        self,
        project_id: str,
        analysis: ProjectAnalysis,
        generated_files: Dict[str, str],
    ) -> List[ModificationSuggestion]:
        """Generate initial improvement suggestions"""
        try:
            # Create a prompt for initial suggestions
            suggestion_prompt = f"""
            Based on the generated project with {len(generated_files)} files, suggest 3-5 potential improvements or additional features that could enhance the project. Consider:
            1. Code quality improvements
            2. Additional features that would be valuable
            3. Performance optimizations
            4. Security enhancements
            5. Developer experience improvements

            Project type: {analysis.project_type}
            Technology stack: {analysis.technology_stack.model_dump_json()}
            """

            suggestions = await self.langgraph_generator.suggest_modifications(
                project_id, suggestion_prompt, generated_files
            )

            return suggestions[:5]  # Limit to 5 initial suggestions

        except Exception:
            # Return empty list if suggestion generation fails
            return []

    async def _apply_modifications(
        self,
        project_id: str,
        modification_ids: List[str],
        custom_changes: Optional[Dict[str, str]] = None,
    ) -> List[str]:
        """Apply modifications to project files"""
        if project_id not in self.modification_suggestions:
            return []

        applied = []
        current_files = self.project_files[project_id]

        # Apply AI suggestions
        for suggestion in self.modification_suggestions[project_id]:
            if suggestion.suggestion_id in modification_ids:
                for file_mod in suggestion.files:
                    if file_mod.action == "create" or file_mod.action == "modify":
                        if file_mod.content:
                            current_files[file_mod.file_path] = file_mod.content
                    elif file_mod.action == "delete":
                        if file_mod.file_path in current_files:
                            del current_files[file_mod.file_path]

                applied.append(suggestion.suggestion_id)

        # Apply custom changes
        if custom_changes:
            for file_path, content in custom_changes.items():
                current_files[file_path] = content

        return applied

    async def _update_project_files(self, project_id: str, project_dir: Path):
        """Update project files on disk"""
        current_files = self.project_files[project_id]

        # Clear existing files
        if project_dir.exists():
            import shutil

            shutil.rmtree(project_dir)
        project_dir.mkdir(exist_ok=True)

        # Write updated files atomically
        import tempfile
        import shutil

        for file_path, content in current_files.items():
            full_path = project_dir / file_path
            full_path.parent.mkdir(parents=True, exist_ok=True)

            # Write to temporary file first, then move to final location
            with tempfile.NamedTemporaryFile(
                mode="w",
                encoding="utf-8",
                delete=False,
                dir=full_path.parent,
                suffix=".tmp",
            ) as temp_file:
                temp_file.write(content)
                temp_file.flush()

            # Atomic move to final location
            shutil.move(temp_file.name, full_path)

    async def _create_zip_file(self, project_id: str, project_dir: Path) -> str:
        """Create a ZIP file of the generated project"""
        zip_path = self.output_dir / f"{project_id}.zip"

        with zipfile.ZipFile(zip_path, "w", zipfile.ZIP_DEFLATED) as zipf:
            for file_path in project_dir.rglob("*"):
                if file_path.is_file():
                    arcname = file_path.relative_to(project_dir)
                    zipf.write(file_path, arcname)

        return str(zip_path)

    def get_enhanced_generation_status(
        self, project_id: str
    ) -> Optional[EnhancedGenerationStatus]:
        """Get the enhanced generation status for a project"""
        return self.generation_status.get(project_id)

    def get_project_analysis(self, project_id: str) -> Optional[ProjectAnalysis]:
        """Get the AI analysis for a project"""
        return self.project_analyses.get(project_id)

    def get_modification_suggestions(
        self, project_id: str
    ) -> List[ModificationSuggestion]:
        """Get modification suggestions for a project"""
        return self.modification_suggestions.get(project_id, [])

    def get_download_path(self, project_id: str) -> Optional[str]:
        """Get the download path for a completed project"""
        zip_path = self.output_dir / f"{project_id}.zip"
        return str(zip_path) if zip_path.exists() else None

    def _generate_setup_instructions(self, analysis: ProjectAnalysis) -> str:
        """Generate setup instructions based on technology stack"""
        tech_stack = analysis.technology_stack
        instructions = []

        if tech_stack.frontend and any(
            "react" in tech.lower() for tech in tech_stack.frontend
        ):
            instructions.extend(
                [
                    "## Frontend Setup",
                    "1. Navigate to frontend directory: `cd frontend`",
                    "2. Install dependencies: `npm install`",
                    "3. Start development server: `npm run dev`",
                    "4. Open http://localhost:3000",
                ]
            )

        if tech_stack.backend and any(
            "fastapi" in tech.lower() or "python" in tech.lower()
            for tech in tech_stack.backend
        ):
            instructions.extend(
                [
                    "## Backend Setup",
                    "1. Navigate to backend directory: `cd backend`",
                    "2. Create virtual environment: `python -m venv venv`",
                    "3. Activate virtual environment: `source venv/bin/activate` (Linux/Mac) or `venv\\Scripts\\activate` (Windows)",
                    "4. Install dependencies: `pip install -r requirements.txt`",
                    "5. Start server: `uvicorn main:app --reload`",
                    "6. API available at http://localhost:8000",
                ]
            )

        return (
            "\n".join(instructions)
            if instructions
            else "1. Follow standard setup for your chosen technology stack"
        )

    def _generate_next_steps(self, analysis: ProjectAnalysis) -> List[str]:
        """Generate next steps based on analysis"""
        steps = ["Install dependencies", "Configure environment variables"]

        if analysis.project_type == "fullstack":
            steps.extend(["Start backend server", "Start frontend development server"])
        elif analysis.project_type == "frontend":
            steps.append("Start development server")
        elif analysis.project_type == "backend":
            steps.append("Start API server")

        if analysis.technology_stack.database:
            steps.append("Set up database connection")

        steps.extend(["Review generated code", "Customize as needed"])
        return steps
